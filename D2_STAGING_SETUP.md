# 🚀 D2 Staging Setup - MCP Rules Engine Feature Flag System

## Overview

D2 phase implements staging deployment with feature flag controls for the MCP Rules Engine integration. This setup enables controlled testing and validation before pilot tenant deployment.

## ✅ D2 Implementation Complete

### 🎯 **What's Been Implemented:**

#### 1. **Feature Flag System**
- ✅ Centralized feature flag service (`frontend/src/lib/featureFlags.ts`)
- ✅ Environment-based configuration
- ✅ Tenant-specific controls for D3 pilot phase
- ✅ Deployment phase detection

#### 2. **Environment Configuration**
- ✅ D2 staging environment file (`.env.d2.staging`)
- ✅ D3 pilot environment file (`.env.d3.pilot`)
- ✅ Updated main configuration with MCP settings
- ✅ Vercel preview deployment support

#### 3. **Enhanced MCP Service**
- ✅ Updated `McpService` with feature flag integration
- ✅ Tenant-specific enablement logic
- ✅ Enhanced logging and metrics collection
- ✅ D2/D3 phase-aware functionality

#### 4. **Monitoring & Dashboard**
- ✅ Feature flag dashboard component
- ✅ Real-time deployment phase monitoring
- ✅ Performance metrics tracking
- ✅ Debug information display

#### 5. **Deployment Scripts**
- ✅ Automated D2 staging deployment script
- ✅ Monitoring script for performance validation
- ✅ Rollback procedures

## 🔧 **Configuration Files**

### Environment Variables Added:
```bash
# Feature Flags
NEXT_PUBLIC_FEATURE_MCP_RULES_ENGINE=false
NEXT_PUBLIC_D2_STAGING_MODE=false
NEXT_PUBLIC_D3_PILOT_TENANT=false

# MCP Configuration
NEXT_PUBLIC_MCP_RULES_BASE_URL=https://rules.ailexlaw.com
MCP_API_KEY=your-mcp-api-key-here
NEXT_PUBLIC_MCP_API_TIMEOUT=30000
NEXT_PUBLIC_MCP_MAX_RETRIES=3
NEXT_PUBLIC_MCP_RETRY_DELAY=1000

# Monitoring
NEXT_PUBLIC_MCP_DEBUG_LOGGING=true
NEXT_PUBLIC_MCP_METRICS_ENABLED=true
```

## 🚀 **D2 Deployment Process**

### **Step 1: Run D2 Deployment Script**
```bash
./scripts/deploy-d2-staging.sh
```

This script will:
- ✅ Create `d2-staging-deployment` branch
- ✅ Configure staging environment variables
- ✅ Validate TypeScript compilation
- ✅ Push to remote for Vercel preview deployment

### **Step 2: Monitor Staging Performance**
```bash
./scripts/monitor-d2-staging.sh https://your-preview-url.vercel.app
```

This will monitor:
- ✅ API health status
- ✅ MCP Rules Engine integration
- ✅ Error rates (target: <1%)
- ✅ Latency (target: <700ms)
- ✅ Feature flag status

## 📊 **Feature Flag Usage**

### **In React Components:**
```typescript
import { useFeatureFlags } from '@/lib/featureFlags';

const MyComponent = () => {
  const { mcpFlags, isMcpEnabled } = useFeatureFlags({
    tenantId: 'pilot-smith'
  });

  if (isMcpEnabled) {
    // Use MCP Rules Engine
    return <McpEnabledComponent />;
  }
  
  // Fallback to existing logic
  return <LegacyComponent />;
};
```

### **In Backend Services:**
```typescript
import { mcpService } from '@/services/mcpService';

// Service automatically checks feature flags
const deadlines = await mcpService.calculateCaseDeadlines(
  request,
  tenantId // Used for D3 pilot tenant checking
);
```

## 🎯 **D2 Success Criteria**

### **Performance Targets:**
- ✅ Error Rate: < 1%
- ✅ P95 Latency: < 700ms
- ✅ Feature Flag Toggle: Working
- ✅ Staging Environment: Stable

### **Validation Checklist:**
- [ ] Vercel preview deployment successful
- [ ] MCP Rules Engine responds correctly
- [ ] Error handling works as expected
- [ ] Retry logic functions properly
- [ ] Metrics collection active
- [ ] Feature flags toggle correctly
- [ ] Debug logging captures issues

## 🔄 **Deployment Phases**

### **Current Phase: D2 Staging**
- **Environment**: Vercel Preview
- **Feature Flag**: `D2_STAGING_MODE=true`
- **Scope**: Staging validation only
- **Duration**: 1 day

### **Next Phase: D3 Pilot**
- **Environment**: Production
- **Feature Flag**: `D3_PILOT_TENANT=true`
- **Scope**: pilot-smith tenant only
- **Duration**: 3 days

## 🛡️ **Safety Measures**

### **Rollback Plan:**
1. **Immediate**: Disable feature flags via environment variables
2. **Quick**: Revert to main branch deployment
3. **Emergency**: Circuit breaker will trigger automatically

### **Monitoring Alerts:**
- Error rate exceeding 1%
- Latency exceeding 700ms
- Circuit breaker activation
- Feature flag toggle failures

## 🔍 **Troubleshooting**

### **Common Issues:**

#### **Feature Flag Not Working:**
```bash
# Check environment variables
echo $NEXT_PUBLIC_FEATURE_MCP_RULES_ENGINE
echo $NEXT_PUBLIC_D2_STAGING_MODE

# Verify in browser console
console.log(window.location.href);
// Should show preview URL for staging
```

#### **MCP Integration Failing:**
```bash
# Check MCP service logs
# Look for [McpService] debug messages
# Verify API key configuration
```

#### **Performance Issues:**
```bash
# Run monitoring script
./scripts/monitor-d2-staging.sh https://your-url.vercel.app

# Check specific metrics
curl -s https://your-url.vercel.app/api/health
```

## 📋 **Next Steps for D3**

Once D2 validation passes:

1. **Update Environment Variables:**
   ```bash
   NEXT_PUBLIC_D2_STAGING_MODE=false
   NEXT_PUBLIC_D3_PILOT_TENANT=true
   ```

2. **Deploy to Production:**
   - Merge staging branch to main
   - Deploy to production Vercel
   - Enable for pilot-smith tenant only

3. **Monitor Pilot Usage:**
   - Real-world case creation
   - User feedback collection
   - Performance under load

## 🎉 **D2 Setup Complete!**

The feature flag system is now ready for D2 staging deployment. Run the deployment script when you're ready to proceed with staging validation.

**Ready for D2 deployment?** 🚀
