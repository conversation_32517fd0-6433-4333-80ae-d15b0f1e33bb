/**
 * MCP Rules Engine Service Integration
 * 
 * Service layer for integrating MCP Rules Engine into case creation flow
 * with feature flag support and error handling.
 */

import { logMcpOperation } from '../middleware/mcpMetrics';

// MCP Types - in production these would be imported from @ailex/mcp-client
interface DeadlineResponse {
  deadlines: Deadline[];
  jurisdiction: string;
  triggerCode: string;
  startDate: string;
  practiceArea?: string;
}

interface Deadline {
  id: string;
  name: string;
  description: string;
  dueDate: string;
  priority: 'high' | 'medium' | 'low';
  category: string;
  isStatutory: boolean;
  source?: string;
  notes?: string;
}

interface McpClientConfig {
  baseUrl: string;
  apiKey: string;
  timeout?: number;
  maxRetries?: number;
  retryDelay?: number;
}

// Mock MCP Client for TypeScript compilation - replace with actual import in production
class McpClient {
  constructor(config: McpClientConfig) {
    // Mock implementation
  }

  async calculateDeadlines(
    jurisdiction: string,
    triggerCode: string,
    startDate: string,
    practiceArea?: string
  ): Promise<DeadlineResponse> {
    throw new Error('Mock implementation - replace with actual MCP client');
  }

  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy' }> {
    throw new Error('Mock implementation - replace with actual MCP client');
  }
}

class McpApiError extends Error {
  public readonly status: number;
  public readonly code?: string;
  public readonly details?: any;

  constructor(message: string, status: number, code?: string, details?: any) {
    super(message);
    this.name = 'McpApiError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

interface CaseDeadlineRequest {
  tenantId: string;
  jurisdiction: string;
  incidentDate: string;
  caseType?: string;
  practiceArea?: string;
}

interface ProcessedDeadline {
  id: string;
  name: string;
  description: string;
  dueDate: Date;
  priority: 'high' | 'medium' | 'low';
  category: string;
  isStatutory: boolean;
  daysFromIncident: number;
  source?: string;
  notes?: string;
}

export class McpService {
  private client: McpClient;
  private isEnabled: boolean;
  private debugLogging: boolean;
  private metricsEnabled: boolean;

  constructor() {
    this.client = new McpClient({
      baseUrl: process.env.MCP_RULES_BASE || process.env.NEXT_PUBLIC_MCP_RULES_BASE_URL || 'https://rules.ailexlaw.com',
      apiKey: process.env.MCP_API_KEY || '',
      timeout: parseInt(process.env.NEXT_PUBLIC_MCP_API_TIMEOUT || '30000'),
      maxRetries: parseInt(process.env.NEXT_PUBLIC_MCP_MAX_RETRIES || '3'),
      retryDelay: parseInt(process.env.NEXT_PUBLIC_MCP_RETRY_DELAY || '1000'),
    });

    // D2 Feature flags for MCP Rules Engine
    this.isEnabled = process.env.FEATURE_MCP_RULES_ENGINE === 'true' ||
                     process.env.NEXT_PUBLIC_FEATURE_MCP_RULES_ENGINE === 'true';
    this.debugLogging = process.env.NEXT_PUBLIC_MCP_DEBUG_LOGGING === 'true';
    this.metricsEnabled = process.env.NEXT_PUBLIC_MCP_METRICS_ENABLED !== 'false';

    if (this.debugLogging) {
      console.log('[McpService] Initialized with config:', {
        enabled: this.isEnabled,
        baseUrl: this.client['baseUrl'],
        debugLogging: this.debugLogging,
        metricsEnabled: this.metricsEnabled,
        deploymentPhase: process.env.NEXT_PUBLIC_D2_STAGING_MODE === 'true' ? 'D2_STAGING' :
                        process.env.NEXT_PUBLIC_D3_PILOT_TENANT === 'true' ? 'D3_PILOT' : 'PRODUCTION'
      });
    }
  }

  /**
   * Calculate deadlines for a new case with D2 feature flag support
   */
  async calculateCaseDeadlines(request: CaseDeadlineRequest, tenantId?: string): Promise<ProcessedDeadline[]> {
    // D2/D3 Feature flag check with tenant-specific logic
    if (!this.isFeatureEnabledForTenant(tenantId)) {
      if (this.debugLogging) {
        console.log('[McpService] MCP Rules Engine disabled for tenant:', { tenantId, enabled: this.isEnabled });
      }
      return [];
    }

    const startTime = Date.now();
    let success = false;
    let errorMessage: string | undefined;

    if (this.debugLogging) {
      console.log('[McpService] Starting deadline calculation:', {
        request,
        tenantId,
        timestamp: new Date().toISOString()
      });
    }

    try {
      // Determine trigger code based on case type
      const triggerCode = this.mapCaseTypeToTriggerCode(request.caseType);
      
      console.log(`Calculating deadlines for tenant ${request.tenantId}`, {
        jurisdiction: request.jurisdiction,
        triggerCode,
        incidentDate: request.incidentDate,
        practiceArea: request.practiceArea,
      });

      const response = await this.client.calculateDeadlines(
        request.jurisdiction,
        triggerCode,
        request.incidentDate,
        request.practiceArea
      );

      const processedDeadlines = this.processDeadlineResponse(response, request.incidentDate);
      success = true;

      console.log(`Successfully calculated ${processedDeadlines.length} deadlines for tenant ${request.tenantId}`);
      
      return processedDeadlines;

    } catch (error) {
      success = false;
      
      if (error instanceof McpApiError) {
        errorMessage = `MCP API Error ${error.status}: ${error.message}`;
        console.error(errorMessage, {
          tenantId: request.tenantId,
          status: error.status,
          code: error.code,
          details: error.details,
          circuitBreakerState: this.client.getCircuitBreakerState(),
        });

        // Handle circuit breaker open state gracefully
        if (error.code === 'CIRCUIT_BREAKER_OPEN') {
          console.warn(`Circuit breaker is open for tenant ${request.tenantId}, returning empty deadlines`);
          return [];
        }
      } else {
        errorMessage = `Network or system error: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(errorMessage, {
          tenantId: request.tenantId,
          error: error instanceof Error ? error.stack : error,
          circuitBreakerState: this.client.getCircuitBreakerState(),
        });
      }

      // For non-critical errors, return empty array instead of throwing
      if (error instanceof McpApiError && error.status >= 400 && error.status < 500) {
        console.warn(`Non-critical MCP error for tenant ${request.tenantId}, continuing without deadlines`);
        return [];
      }

      // For server errors, we might want to throw to trigger retry at a higher level
      throw error;

    } finally {
      const latencyMs = Date.now() - startTime;
      
      // Log metrics
      logMcpOperation(
        request.tenantId,
        'calculate_case_deadlines',
        success,
        latencyMs,
        {
          jurisdiction: request.jurisdiction,
          triggerCode: this.mapCaseTypeToTriggerCode(request.caseType),
          errorMessage,
        }
      );
    }
  }

  /**
   * D2/D3 Feature flag check with tenant-specific logic
   */
  private isFeatureEnabledForTenant(tenantId?: string): boolean {
    if (!this.isEnabled) {
      return false;
    }

    // D3 Pilot Mode: Only enabled for specific tenants
    if (process.env.NEXT_PUBLIC_D3_PILOT_TENANT === 'true') {
      const pilotTenants = ['pilot-smith', 'test-tenant-1', 'dev-tenant'];
      return tenantId ? pilotTenants.includes(tenantId) : false;
    }

    // D2 Staging Mode: Enabled in staging environment
    if (process.env.NEXT_PUBLIC_D2_STAGING_MODE === 'true') {
      return process.env.VERCEL_ENV === 'preview' || process.env.NODE_ENV === 'development';
    }

    // Global feature flag
    return true;
  }

  /**
   * Log metrics for monitoring (D2 requirement)
   */
  private logMetrics(operation: string, duration: number, success: boolean, error?: string) {
    if (!this.metricsEnabled) return;

    const metrics = {
      operation,
      duration,
      success,
      error,
      timestamp: new Date().toISOString(),
      deploymentPhase: process.env.NEXT_PUBLIC_D2_STAGING_MODE === 'true' ? 'D2_STAGING' :
                      process.env.NEXT_PUBLIC_D3_PILOT_TENANT === 'true' ? 'D3_PILOT' : 'PRODUCTION'
    };

    if (this.debugLogging) {
      console.log('[McpService] Metrics:', metrics);
    }

    // In production, this would send to monitoring service
    // For D2, we're logging to console for validation
  }

  /**
   * Check if MCP Rules Engine is healthy
   */
  async checkHealth(): Promise<boolean> {
    if (!this.isEnabled) {
      return false;
    }

    try {
      const health = await this.client.healthCheck();
      return health.status === 'healthy';
    } catch (error) {
      console.error('MCP health check failed:', error);
      return false;
    }
  }

  /**
   * Map case type to MCP trigger code
   */
  private mapCaseTypeToTriggerCode(caseType?: string): string {
    const mapping: Record<string, string> = {
      'motor-vehicle-accident': 'ACCIDENT',
      'slip-and-fall': 'PREMISES_LIABILITY',
      'medical-malpractice': 'MEDICAL_MALPRACTICE',
      'product-liability': 'PRODUCT_LIABILITY',
      'wrongful-death': 'WRONGFUL_DEATH',
      'workers-compensation': 'WORKERS_COMP',
    };

    return mapping[caseType || ''] || 'GENERAL_INJURY';
  }

  /**
   * Process raw deadline response into structured format
   */
  private processDeadlineResponse(
    response: DeadlineResponse,
    incidentDate: string
  ): ProcessedDeadline[] {
    const incidentDateTime = new Date(incidentDate);

    return response.deadlines.map(deadline => {
      const dueDate = new Date(deadline.dueDate);
      const daysFromIncident = Math.ceil(
        (dueDate.getTime() - incidentDateTime.getTime()) / (1000 * 60 * 60 * 24)
      );

      return {
        id: deadline.id,
        name: deadline.name,
        description: deadline.description,
        dueDate,
        priority: deadline.priority,
        category: deadline.category,
        isStatutory: deadline.isStatutory,
        daysFromIncident,
        source: deadline.source,
        notes: deadline.notes,
      };
    });
  }

  /**
   * Get feature flag status
   */
  isFeatureEnabled(): boolean {
    return this.isEnabled;
  }

  /**
   * Enable or disable the feature (for testing)
   */
  setFeatureEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }
}

// Export singleton instance
export const mcpService = new McpService();
