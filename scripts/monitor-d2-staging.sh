#!/bin/bash

# D2 Staging Monitoring Script
# Monitors MCP Rules Engine performance and feature flag status during D2 phase

set -e

echo "📊 D2 Staging Monitoring Dashboard"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
STAGING_URL=${1:-"https://your-staging-url.vercel.app"}
CHECK_INTERVAL=${2:-30}
MAX_CHECKS=${3:-20}

print_status() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')]${NC} $1"
}

print_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')]${NC} $1"
}

# Function to check API health
check_api_health() {
    local url="$1/api/health"
    local response=$(curl -s -w "%{http_code}" -o /dev/null "$url" 2>/dev/null || echo "000")
    echo "$response"
}

# Function to check MCP feature flag status
check_feature_flags() {
    local url="$1/api/admin/feature-flags"
    curl -s "$url" 2>/dev/null || echo '{"error": "Unable to fetch feature flags"}'
}

# Function to simulate MCP deadline calculation
test_mcp_integration() {
    local url="$1/api/cases"
    local payload='{"caseType":"motor-vehicle-accident","jurisdiction":"TX","incidentDate":"2024-01-15","tenantId":"test-tenant"}'
    
    local start_time=$(date +%s%3N)
    local response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d "$payload" \
        "$url" 2>/dev/null || echo "000")
    local end_time=$(date +%s%3N)
    local latency=$((end_time - start_time))
    
    echo "$response:$latency"
}

# Main monitoring loop
print_status "Starting D2 staging monitoring..."
print_status "Staging URL: $STAGING_URL"
print_status "Check interval: ${CHECK_INTERVAL}s"
print_status "Max checks: $MAX_CHECKS"
echo ""

# Performance tracking
total_requests=0
successful_requests=0
failed_requests=0
total_latency=0
max_latency=0
min_latency=999999

for i in $(seq 1 $MAX_CHECKS); do
    echo "=================================================="
    print_status "Check #$i of $MAX_CHECKS"
    
    # Check API health
    health_status=$(check_api_health "$STAGING_URL")
    if [ "$health_status" = "200" ]; then
        print_success "API Health: OK"
    else
        print_error "API Health: FAILED (HTTP $health_status)"
    fi
    
    # Test MCP integration
    print_status "Testing MCP Rules Engine integration..."
    mcp_result=$(test_mcp_integration "$STAGING_URL")
    http_code=$(echo "$mcp_result" | cut -d':' -f1)
    latency=$(echo "$mcp_result" | cut -d':' -f2)
    
    total_requests=$((total_requests + 1))
    
    if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
        successful_requests=$((successful_requests + 1))
        total_latency=$((total_latency + latency))
        
        if [ "$latency" -gt "$max_latency" ]; then
            max_latency=$latency
        fi
        
        if [ "$latency" -lt "$min_latency" ]; then
            min_latency=$latency
        fi
        
        if [ "$latency" -lt 700 ]; then
            print_success "MCP Request: OK (${latency}ms) ✅"
        else
            print_warning "MCP Request: SLOW (${latency}ms) ⚠️"
        fi
    else
        failed_requests=$((failed_requests + 1))
        print_error "MCP Request: FAILED (HTTP $http_code) ❌"
    fi
    
    # Calculate current metrics
    if [ "$total_requests" -gt 0 ]; then
        error_rate=$(echo "scale=2; $failed_requests * 100 / $total_requests" | bc -l 2>/dev/null || echo "0")
        if [ "$successful_requests" -gt 0 ]; then
            avg_latency=$((total_latency / successful_requests))
        else
            avg_latency=0
        fi
        
        echo ""
        echo "📊 Current Metrics:"
        echo "  • Total Requests: $total_requests"
        echo "  • Success Rate: $(echo "scale=1; $successful_requests * 100 / $total_requests" | bc -l 2>/dev/null || echo "0")%"
        echo "  • Error Rate: ${error_rate}% (Target: <1%)"
        echo "  • Avg Latency: ${avg_latency}ms"
        echo "  • Max Latency: ${max_latency}ms (Target: <700ms)"
        echo "  • Min Latency: ${min_latency}ms"
        
        # Check if we're meeting D2 targets
        if (( $(echo "$error_rate < 1" | bc -l 2>/dev/null || echo "0") )) && [ "$avg_latency" -lt 700 ]; then
            print_success "✅ Meeting D2 performance targets!"
        else
            print_warning "⚠️  Not meeting D2 performance targets"
        fi
    fi
    
    # Check feature flags status
    print_status "Checking feature flag status..."
    feature_flags=$(check_feature_flags "$STAGING_URL")
    if echo "$feature_flags" | grep -q "MCP_RULES_ENGINE.*true"; then
        print_success "Feature Flag: MCP Rules Engine ENABLED ✅"
    else
        print_warning "Feature Flag: MCP Rules Engine status unclear ⚠️"
    fi
    
    echo ""
    
    # Wait before next check (except for last iteration)
    if [ "$i" -lt "$MAX_CHECKS" ]; then
        print_status "Waiting ${CHECK_INTERVAL}s before next check..."
        sleep $CHECK_INTERVAL
    fi
done

# Final summary
echo "=================================================="
echo -e "${GREEN}📋 D2 STAGING MONITORING SUMMARY${NC}"
echo "=================================================="
echo ""
echo "🔢 Overall Statistics:"
echo "  • Total Requests: $total_requests"
echo "  • Successful: $successful_requests"
echo "  • Failed: $failed_requests"
if [ "$total_requests" -gt 0 ]; then
    final_error_rate=$(echo "scale=2; $failed_requests * 100 / $total_requests" | bc -l 2>/dev/null || echo "0")
    echo "  • Final Error Rate: ${final_error_rate}%"
    
    if [ "$successful_requests" -gt 0 ]; then
        final_avg_latency=$((total_latency / successful_requests))
        echo "  • Average Latency: ${final_avg_latency}ms"
        echo "  • Max Latency: ${max_latency}ms"
        echo "  • Min Latency: ${min_latency}ms"
    fi
fi

echo ""
echo "🎯 D2 Target Assessment:"
if [ "$total_requests" -gt 0 ] && (( $(echo "$final_error_rate < 1" | bc -l 2>/dev/null || echo "0") )); then
    print_success "✅ Error Rate Target: PASSED (<1%)"
else
    print_error "❌ Error Rate Target: FAILED (≥1%)"
fi

if [ "$successful_requests" -gt 0 ] && [ "$final_avg_latency" -lt 700 ]; then
    print_success "✅ Latency Target: PASSED (<700ms)"
else
    print_error "❌ Latency Target: FAILED (≥700ms)"
fi

echo ""
echo "📋 Next Steps:"
if [ "$total_requests" -gt 0 ] && (( $(echo "$final_error_rate < 1" | bc -l 2>/dev/null || echo "0") )) && [ "$final_avg_latency" -lt 700 ]; then
    print_success "🚀 D2 validation PASSED - Ready for D3 pilot testing!"
    echo "  • Proceed with D3 pilot tenant deployment"
    echo "  • Enable feature flag for pilot-smith tenant"
    echo "  • Begin real-world usage monitoring"
else
    print_warning "⚠️  D2 validation needs attention"
    echo "  • Review error logs and performance issues"
    echo "  • Optimize MCP integration if needed"
    echo "  • Re-run monitoring before proceeding to D3"
fi

echo ""
print_status "D2 staging monitoring completed."
