'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { 
  Settings, 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Users,
  BarChart3,
  Shield,
  Zap
} from 'lucide-react';
import { useFeatureFlags } from '@/lib/featureFlags';
import { DEPLOYMENT_PHASE } from '@/lib/config';
import { FeatureFlagDashboard } from '@/components/superadmin/FeatureFlagDashboard';

export default function McpDeploymentPage() {
  const { toast } = useToast();
  const { mcpFlags, deploymentInfo, isMcpEnabled } = useFeatureFlags();
  const [metrics, setMetrics] = useState({
    totalRequests: 0,
    successRate: 0,
    avgLatency: 0,
    errorRate: 0,
    lastUpdated: new Date()
  });

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'D0_DEVELOPMENT': return 'bg-gray-100 text-gray-800';
      case 'D1_READY': return 'bg-blue-100 text-blue-800';
      case 'D2_STAGING': return 'bg-yellow-100 text-yellow-800';
      case 'D3_PILOT': return 'bg-orange-100 text-orange-800';
      default: return 'bg-green-100 text-green-800';
    }
  };

  const getStatusColor = (enabled: boolean) => {
    return enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  const getPhaseDescription = (phase: string) => {
    switch (phase) {
      case 'D0_DEVELOPMENT': 
        return 'Development phase - MCP integration in development';
      case 'D1_READY': 
        return 'Integration complete - Ready for staging deployment';
      case 'D2_STAGING': 
        return 'Staging validation - Testing in preview environment';
      case 'D3_PILOT': 
        return 'Pilot testing - Enabled for pilot-smith tenant only';
      default: 
        return 'Production deployment - Fully operational';
    }
  };

  // Simulate metrics fetching (replace with real API call)
  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        // This would be a real API call to your monitoring endpoint
        // const response = await fetch('/api/admin/mcp-metrics');
        // const data = await response.json();
        
        // Simulated data for now
        setMetrics({
          totalRequests: 1247,
          successRate: 99.2,
          avgLatency: 342,
          errorRate: 0.8,
          lastUpdated: new Date()
        });
      } catch (error) {
        console.error('Error fetching MCP metrics:', error);
      }
    };

    fetchMetrics();
    const interval = setInterval(fetchMetrics, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const handleToggleFeatureFlag = async (flagName: string, enabled: boolean) => {
    try {
      // This would be an API call to toggle the feature flag
      // await fetch('/api/admin/feature-flags', {
      //   method: 'POST',
      //   body: JSON.stringify({ flag: flagName, enabled })
      // });
      
      toast({
        title: 'Feature Flag Updated',
        description: `${flagName} has been ${enabled ? 'enabled' : 'disabled'}`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update feature flag',
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold">MCP Rules Engine Deployment</h1>
          <p className="text-gray-500">
            Monitor and control the MCP Rules Engine phased deployment
          </p>
        </div>
        <Badge className={getPhaseColor(DEPLOYMENT_PHASE)}>
          {DEPLOYMENT_PHASE}
        </Badge>
      </div>

      {/* Current Phase Status */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Current Deployment Phase
          </CardTitle>
          <CardDescription>
            {getPhaseDescription(DEPLOYMENT_PHASE)}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${isMcpEnabled ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm">
                MCP Rules Engine: {isMcpEnabled ? 'ACTIVE' : 'INACTIVE'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${mcpFlags.metricsEnabled ? 'bg-green-500' : 'bg-gray-400'}`} />
              <span className="text-sm">
                Metrics: {mcpFlags.metricsEnabled ? 'COLLECTING' : 'DISABLED'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${mcpFlags.debugLogging ? 'bg-yellow-500' : 'bg-gray-400'}`} />
              <span className="text-sm">
                Debug Logging: {mcpFlags.debugLogging ? 'ENABLED' : 'DISABLED'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="metrics">Performance Metrics</TabsTrigger>
          <TabsTrigger value="flags">Feature Flags</TabsTrigger>
          <TabsTrigger value="deployment">Deployment Control</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalRequests.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">Since deployment</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{metrics.successRate}%</div>
                <p className="text-xs text-muted-foreground">Target: &gt;99%</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Latency</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${metrics.avgLatency < 700 ? 'text-green-600' : 'text-red-600'}`}>
                  {metrics.avgLatency}ms
                </div>
                <p className="text-xs text-muted-foreground">Target: &lt;700ms</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${metrics.errorRate < 1 ? 'text-green-600' : 'text-red-600'}`}>
                  {metrics.errorRate}%
                </div>
                <p className="text-xs text-muted-foreground">Target: &lt;1%</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="metrics">
          <Card>
            <CardHeader>
              <CardTitle>Performance Monitoring</CardTitle>
              <CardDescription>
                Real-time performance metrics for MCP Rules Engine
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Meeting D2 Performance Targets:</span>
                  <Badge variant={metrics.errorRate < 1 && metrics.avgLatency < 700 ? "default" : "destructive"}>
                    {metrics.errorRate < 1 && metrics.avgLatency < 700 ? "PASSING" : "NEEDS ATTENTION"}
                  </Badge>
                </div>
                <div className="text-sm text-gray-600">
                  Last updated: {metrics.lastUpdated.toLocaleTimeString()}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="flags">
          <FeatureFlagDashboard />
        </TabsContent>

        <TabsContent value="deployment">
          <Card>
            <CardHeader>
              <CardTitle>Deployment Control</CardTitle>
              <CardDescription>
                Manage the MCP Rules Engine deployment phases
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border rounded bg-yellow-50">
                  <h4 className="font-medium text-yellow-800">⚠️ Deployment Phase Control</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    Deployment phase transitions should be managed through environment variables and deployment scripts.
                    Manual overrides are available for emergency situations only.
                  </p>
                </div>
                
                <div className="grid gap-4">
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <div className="font-medium">Emergency Disable</div>
                      <div className="text-sm text-gray-500">
                        Immediately disable MCP Rules Engine across all environments
                      </div>
                    </div>
                    <Button 
                      variant="destructive" 
                      size="sm"
                      onClick={() => handleToggleFeatureFlag('MCP_RULES_ENGINE', false)}
                    >
                      Emergency Disable
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
