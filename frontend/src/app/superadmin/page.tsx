'use client';

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Users, FileText, Database, Shield, Settings, Activity } from 'lucide-react';
import Link from 'next/link';

export default function AdminDashboard() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    tenantCount: 0,
    userCount: 0,
    documentCount: 0,
    processingCount: 0
  });

  const fetchDashboardStats = useCallback(async () => {
    setLoading(true);
    try {
      // Get tenant count
      const { count: tenantCount, error: tenantError } = await supabase
        .schema('tenants')
        .from('firms')
        .select('*', { count: 'exact', head: true });

      if (tenantError) throw tenantError;

      // Get user count
      const { count: userCount, error: userError } = await supabase
        .schema('tenants')
        .from('users')
        .select('*', { count: 'exact', head: true });

      if (userError) throw userError;

      // Get document count
      const { count: documentCount, error: documentError } = await supabase
        .schema('tenants')
        .from('case_documents')
        .select('*', { count: 'exact', head: true });

      if (documentError) throw documentError;

      // Get processing jobs count
      const { count: processingCount, error: processingError } = await supabase
        .from('document_processing_jobs')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'processing');

      if (processingError) throw processingError;

      setStats({
        tenantCount: tenantCount || 0,
        userCount: userCount || 0,
        documentCount: documentCount || 0,
        processingCount: processingCount || 0
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error fetching dashboard stats';
      console.error('Error fetching dashboard stats:', errorMessage, error);
      toast({
        title: 'Error',
        description: 'Failed to load dashboard statistics',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  }, [supabase, toast]);

  useEffect(() => {
    fetchDashboardStats();
  }, [fetchDashboardStats]);

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Admin Dashboard</h1>
      <p className="text-gray-500 mb-6">
        System-wide overview and management tools.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Link href="/admin/users">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Users
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : stats.userCount}
              </div>
              <p className="text-xs text-muted-foreground">
                Across all tenants
              </p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/tenant-quotas">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Tenants
              </CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : stats.tenantCount}
              </div>
              <p className="text-xs text-muted-foreground">
                Manage tenant quotas
              </p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/resource-usage">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Documents
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : stats.documentCount}
              </div>
              <p className="text-xs text-muted-foreground">
                View resource usage
              </p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/database">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Processing Jobs
              </CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : stats.processingCount}
              </div>
              <p className="text-xs text-muted-foreground">
                Currently active
              </p>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* MCP Deployment Monitoring Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Link href="/superadmin/mcp-deployment">
          <Card className="hover:shadow-md transition-shadow cursor-pointer border-blue-200 bg-blue-50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-900">
                MCP Rules Engine
              </CardTitle>
              <Activity className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-900">D2 STAGING</div>
              <p className="text-xs text-blue-700">
                Monitor deployment phases
              </p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/superadmin/security">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Security Center
              </CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">SECURE</div>
              <p className="text-xs text-muted-foreground">
                System security status
              </p>
            </CardContent>
          </Card>
        </Link>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>System Management</CardTitle>
            <CardDescription>
              Access key administrative functions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="quotas">
              <TabsList className="mb-4">
                <TabsTrigger value="quotas">Tenant Quotas</TabsTrigger>
                <TabsTrigger value="usage">Resource Usage</TabsTrigger>
                <TabsTrigger value="jobs">Processing Jobs</TabsTrigger>
                <TabsTrigger value="mcp">MCP Deployment</TabsTrigger>
              </TabsList>
              <TabsContent value="quotas">
                <div className="p-4 border rounded-md bg-muted/50">
                  <h3 className="text-lg font-medium mb-2">Tenant Quota Management</h3>
                  <p className="mb-4">
                    Control resource allocation for each tenant in the system. Set limits for document uploads,
                    processing, and storage based on their subscription plan.
                  </p>
                  <Link
                    href="/admin/tenant-quotas"
                    className="text-primary hover:underline font-medium"
                  >
                    Manage Tenant Quotas →
                  </Link>
                </div>
              </TabsContent>
              <TabsContent value="usage">
                <div className="p-4 border rounded-md bg-muted/50">
                  <h3 className="text-lg font-medium mb-2">Resource Usage Analytics</h3>
                  <p className="mb-4">
                    Monitor resource consumption across the platform. Track document uploads, processing time,
                    and storage usage to optimize system performance.
                  </p>
                  <Link
                    href="/admin/resource-usage"
                    className="text-primary hover:underline font-medium"
                  >
                    View Resource Analytics →
                  </Link>
                </div>
              </TabsContent>
              <TabsContent value="jobs">
                <div className="p-4 border rounded-md bg-muted/50">
                  <h3 className="text-lg font-medium mb-2">Processing Job Management</h3>
                  <p className="mb-4">
                    Monitor and manage document processing jobs. View active, completed, and failed jobs,
                    and take action on stuck or problematic processing tasks.
                  </p>
                  <Link
                    href="/admin/database"
                    className="text-primary hover:underline font-medium"
                  >
                    Manage Processing Jobs →
                  </Link>
                </div>
              </TabsContent>
              <TabsContent value="mcp">
                <div className="p-4 border rounded-md bg-blue-50 border-blue-200">
                  <h3 className="text-lg font-medium mb-2 text-blue-900">MCP Rules Engine Deployment</h3>
                  <p className="mb-4 text-blue-800">
                    Monitor the phased deployment of MCP Rules Engine integration. Track performance metrics,
                    feature flag status, and deployment phase progression from D2 staging through D7 production.
                  </p>
                  <div className="flex items-center gap-4 mb-4">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <span className="text-sm text-blue-700">Current Phase: D2 Staging</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      <span className="text-sm text-blue-700">Status: Active</span>
                    </div>
                  </div>
                  <Link
                    href="/superadmin/mcp-deployment"
                    className="text-blue-600 hover:underline font-medium"
                  >
                    Monitor MCP Deployment →
                  </Link>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
