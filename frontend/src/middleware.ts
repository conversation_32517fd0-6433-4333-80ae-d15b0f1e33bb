// frontend/src/middleware.ts
import { NextResponse, type NextRequest } from "next/server";
import { UserRole } from "@/lib/types/auth";
import { parseJwtPayload } from "@/lib/supabase/client";
import { getUnifiedSession, getUserRoles } from "@/lib/auth/getUnifiedSession";

// Define protected routes and their required roles (using the standardized UserRole type)
const protectedRoutes: Record<string, UserRole[]> = {
  "/admin": [UserRole.Partner], // Regular admin dashboard (only partners)
  "/dashboard": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ], // Staff dashboard
  "/client-portal": [UserRole.Client], // Client portal
  "/cases": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/documents": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/settings": [UserRole.Admin], // Tenant settings (Tenant Admins only)
  "/billing": [UserRole.Partner], // SaaS Billing (Partners only)
  "/users": [UserRole.Admin], // Tenant user management (Tenant Admins only)
  "/calendar": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/tasks": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/clients": [
    UserRole.Partner,
    UserRole.Attorney,
    UserRole.Paralegal,
    UserRole.Staff,
    UserRole.Admin,
  ],
  "/submit-case": [UserRole.Client],
  // Note: /superadmin is handled separately by JWT is_super_admin claim
};

export async function middleware(request: NextRequest): Promise<NextResponse> {
  const response = NextResponse.next({ request });

  // Get unified session (supports both Supabase and legacy auth)
  const session = await getUnifiedSession(request.cookies);
  const user = session?.user || null;
  console.log(
    "Middleware: User object from supabase.auth.getUser():",
    JSON.stringify(user, null, 2),
  );
  console.log(
    "Middleware: Extracted user_metadata:",
    JSON.stringify(user?.user_metadata, null, 2),
  );
  const currentPath = request.nextUrl.pathname;

  // --- Authentication Check ---
  const isProtectedRoute = Object.keys(protectedRoutes).some((route) =>
    currentPath.startsWith(route),
  );
  const isSuperAdminRoute = currentPath.startsWith("/superadmin");

  if (!user && (isProtectedRoute || isSuperAdminRoute)) {
    const loginUrl = isSuperAdminRoute ? "/loginadmin" : "/login";
    const redirectUrl = new URL(loginUrl, request.url);
    redirectUrl.searchParams.set("redirectedFrom", currentPath);
    console.log(
      `Middleware: Unauthenticated access to ${currentPath}. Redirecting to ${loginUrl}.`,
    );
    return NextResponse.redirect(redirectUrl);
  }

  // --- Authorization Check (only if user is authenticated) ---
  if (user) {
    const userEmail = user.email;

    // 1. Superadmin Route Check (by JWT is_super_admin claim)
    if (isSuperAdminRoute) {
      // Use access token from unified session
      if (!session?.access_token) {
        console.warn(`Middleware: No access token found for superadmin route check. Redirecting.`);
        return NextResponse.redirect(
          new URL("/loginadmin?error=superadmin_unauthorized", request.url),
        );
      }

      // Parse JWT to check is_super_admin claim
      const jwtPayload = parseJwtPayload(session.access_token);
      const isSuperAdmin = jwtPayload?.is_super_admin === true;

      if (!isSuperAdmin) {
        console.warn(
          `Middleware: Unauthorized attempt on /superadmin by ${userEmail}. User is not a super admin. Redirecting.`,
        );
        return NextResponse.redirect(
          new URL("/dashboard?error=superadmin_unauthorized", request.url),
        );
      }

      console.log(`Middleware: Super admin access granted to ${userEmail} (source: ${session.source})`);
      // Allow superadmin access
      return response;
    }

    // 2. Other Protected Routes Check (by role)
    // Get user roles from unified session
    const userRoles = getUserRoles(session);

    // Check if user has valid session and roles
    if (!user?.id) {
      console.log("Middleware: User ID missing, redirecting to login");
      const loginUrl = "/login";
      const redirectUrl = new URL(loginUrl, request.url);
      redirectUrl.searchParams.set("redirectedFrom", currentPath);
      return NextResponse.redirect(redirectUrl);
    }

    // COMPREHENSIVE FIX: Handle admin users and role assignment
    const allowedAdmins = ['<EMAIL>', '<EMAIL>'];
    const isAllowedAdmin = user.email && allowedAdmins.includes(user.email);

    if (userRoles.length === 0) {
      if (isAllowedAdmin) {
        console.log(`Middleware: Assigning default 'partner' role to admin user ${user.email}`);
        userRoles.push('partner');
      } else {
        console.log("Middleware: User roles missing, redirecting to login");
        const loginUrl = "/login";
        const redirectUrl = new URL(loginUrl, request.url);
        redirectUrl.searchParams.set("redirectedFrom", currentPath);
        return NextResponse.redirect(redirectUrl);
      }
    }

    // SPECIAL HANDLING: If this is an allowed admin, ensure they have access to all routes
    if (isAllowedAdmin && !userRoles.includes('partner')) {
      console.log(`Middleware: Ensuring admin user ${user.email} has partner role`);
      userRoles.push('partner');
    }

    // Convert roles to UserRole enums (userRoles may have been modified above)
    const userRoleEnums: UserRole[] = userRoles.map(role => role as UserRole);

    console.log(
      `Middleware: Authenticated user ${user.email} with roles:`,
      userRoleEnums,
    );

    for (const routePrefix in protectedRoutes) {
      if (currentPath.startsWith(routePrefix)) {
        const requiredRoles = protectedRoutes[routePrefix];

        // ADDED DEBUG LOG: What roles did we actually validated?
        console.log(
          `Middleware: Checking path ${currentPath}. User roles validated: [${userRoleEnums.join(
            ", ",
          )}]. Required roles: [${requiredRoles.join(", ")}]`,
        );

        // Check if the user has at least one of the required roles
        const hasRequiredRole = userRoleEnums.some((userRole) =>
          requiredRoles.includes(userRole),
        );

        if (!hasRequiredRole) {
          // ADMIN BYPASS: Allow admin users to access any route
          if (isAllowedAdmin) {
            console.log(`Middleware: Admin user ${userEmail} granted access to ${currentPath} via admin bypass`);
            break; // Skip the role check and allow access
          }

          console.warn(
            `Middleware: Role mismatch for ${currentPath}. User ${userEmail} (roles: ${userRoleEnums.join(",") || "none"}) lacks required roles: ${requiredRoles.join(", ")}. Redirecting.`,
          );

          // PREVENT REDIRECT LOOP: Don't redirect to dashboard if we're already there with an error
          if (currentPath.startsWith("/dashboard") && request.nextUrl.searchParams.has("error")) {
            console.error("Middleware: Redirect loop detected on dashboard. Redirecting to login.");
            return NextResponse.redirect(new URL("/login?error=role_loop", request.url));
          }

          // Redirect unauthorized roles away - but avoid dashboard for users without proper roles
          let defaultRedirect: string;
          if (userRoleEnums.includes(UserRole.Client)) {
            defaultRedirect = "/client-portal";
          } else if (userRoleEnums.length === 0) {
            // No roles at all - redirect to login
            defaultRedirect = "/login";
          } else {
            // Has some roles but not for this route - try dashboard
            defaultRedirect = "/dashboard";
          }

          return NextResponse.redirect(
            new URL(defaultRedirect + "?error=role_unauthorized", request.url),
          );
        }
        // Role is valid for this route, break the loop
        console.log(
          `Middleware: Roles '${userRoleEnums.join(",")}' authorized for ${currentPath}.`,
        );
        break;
      }
    }
  }

  // Allow request to proceed for public routes or authorized users
  return response;
}

// Config to specify which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api (API routes)
     * - auth (auth routes like login, callback, etc.)
     * - Explicitly public routes
     */
    "/((?!_next/static|_next/image|favicon.ico|api|auth|login|loginadmin|register|privacy-policy|terms).*)",
  ],
};
