/**
 * Feature Flag Service for D2-D7 Deployment Phases
 * 
 * Centralized feature flag management for MCP Rules Engine rollout
 * with deployment phase controls and tenant-specific overrides.
 */

import { FEATURE_FLAGS, DEPLOYMENT_PHASE, IS_DEVELOPMENT, IS_STAGING } from './config';

// Types
export interface FeatureFlagContext {
  tenantId?: string;
  userId?: string;
  userRole?: string;
  environment?: 'development' | 'staging' | 'production';
}

export interface McpFeatureFlags {
  enabled: boolean;
  debugLogging: boolean;
  metricsEnabled: boolean;
  stagingMode: boolean;
  pilotTenant: boolean;
}

// Pilot tenant configuration for D3 phase
const PILOT_TENANTS = [
  'pilot-smith', // Primary pilot tenant
  'test-tenant-1', // Additional test tenants
  'dev-tenant'
];

/**
 * Feature Flag Service Class
 */
export class FeatureFlagService {
  private static instance: FeatureFlagService;
  
  private constructor() {}
  
  static getInstance(): FeatureFlagService {
    if (!FeatureFlagService.instance) {
      FeatureFlagService.instance = new FeatureFlagService();
    }
    return FeatureFlagService.instance;
  }

  /**
   * Check if MCP Rules Engine is enabled for the given context
   */
  isMcpRulesEngineEnabled(context: FeatureFlagContext = {}): boolean {
    // Always enabled in development for testing
    if (IS_DEVELOPMENT) {
      return true;
    }

    // D2 Staging Mode: Only enabled in staging environment
    if (FEATURE_FLAGS.D2_STAGING_MODE) {
      return IS_STAGING;
    }

    // D3 Pilot Mode: Only enabled for specific tenants
    if (FEATURE_FLAGS.D3_PILOT_TENANT && context.tenantId) {
      return PILOT_TENANTS.includes(context.tenantId);
    }

    // Global feature flag
    return FEATURE_FLAGS.MCP_RULES_ENGINE;
  }

  /**
   * Get all MCP-related feature flags for the given context
   */
  getMcpFeatureFlags(context: FeatureFlagContext = {}): McpFeatureFlags {
    const enabled = this.isMcpRulesEngineEnabled(context);
    
    return {
      enabled,
      debugLogging: enabled && (FEATURE_FLAGS.MCP_DEBUG_LOGGING || IS_DEVELOPMENT),
      metricsEnabled: enabled && FEATURE_FLAGS.MCP_METRICS_ENABLED,
      stagingMode: FEATURE_FLAGS.D2_STAGING_MODE,
      pilotTenant: FEATURE_FLAGS.D3_PILOT_TENANT && context.tenantId ? 
        PILOT_TENANTS.includes(context.tenantId) : false,
    };
  }

  /**
   * Check if a specific deployment phase is active
   */
  isDeploymentPhaseActive(phase: string): boolean {
    return DEPLOYMENT_PHASE === phase;
  }

  /**
   * Get current deployment phase information
   */
  getDeploymentInfo() {
    return {
      phase: DEPLOYMENT_PHASE,
      environment: IS_DEVELOPMENT ? 'development' : IS_STAGING ? 'staging' : 'production',
      mcpEnabled: FEATURE_FLAGS.MCP_RULES_ENGINE,
      stagingMode: FEATURE_FLAGS.D2_STAGING_MODE,
      pilotMode: FEATURE_FLAGS.D3_PILOT_TENANT,
    };
  }

  /**
   * Log feature flag usage for monitoring
   */
  logFeatureFlagUsage(flagName: string, enabled: boolean, context: FeatureFlagContext = {}) {
    if (FEATURE_FLAGS.MCP_DEBUG_LOGGING || IS_DEVELOPMENT) {
      console.log(`[FeatureFlag] ${flagName}:`, {
        enabled,
        context,
        deploymentPhase: DEPLOYMENT_PHASE,
        timestamp: new Date().toISOString(),
      });
    }
  }
}

// Singleton instance
export const featureFlags = FeatureFlagService.getInstance();

// Convenience functions
export const isMcpEnabled = (context?: FeatureFlagContext) => 
  featureFlags.isMcpRulesEngineEnabled(context);

export const getMcpFlags = (context?: FeatureFlagContext) => 
  featureFlags.getMcpFeatureFlags(context);

export const isD2Staging = () => 
  featureFlags.isDeploymentPhaseActive('D2_STAGING');

export const isD3Pilot = () => 
  featureFlags.isDeploymentPhaseActive('D3_PILOT');

// React Hook for feature flags (if using React)
export const useFeatureFlags = (context: FeatureFlagContext = {}) => {
  return {
    mcpFlags: getMcpFlags(context),
    deploymentInfo: featureFlags.getDeploymentInfo(),
    isMcpEnabled: isMcpEnabled(context),
    isD2Staging: isD2Staging(),
    isD3Pilot: isD3Pilot(),
  };
};
