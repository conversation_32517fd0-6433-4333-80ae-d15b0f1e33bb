/**
 * Configuration constants for the PI Lawyer AI application
 */

// Supabase configuration
export const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
export const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// API configuration
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '/api';
export const LAWS_API_BASE_URL = process.env.NEXT_PUBLIC_LAWS_API_BASE || 'https://legal-api-stg-gfunh6mfpa-uc.a.run.app';

// MCP Rules Engine configuration
export const MCP_RULES_BASE_URL = process.env.NEXT_PUBLIC_MCP_RULES_BASE_URL || 'https://rules.ailexlaw.com';
export const MCP_API_TIMEOUT = parseInt(process.env.NEXT_PUBLIC_MCP_API_TIMEOUT || '30000');
export const MCP_MAX_RETRIES = parseInt(process.env.NEXT_PUBLIC_MCP_MAX_RETRIES || '3');
export const MCP_RETRY_DELAY = parseInt(process.env.NEXT_PUBLIC_MCP_RETRY_DELAY || '1000');

// Feature flags
export const ENABLE_INSIGHTS = process.env.NEXT_PUBLIC_ENABLE_INSIGHTS !== 'false';
export const ENABLE_CHAT = process.env.NEXT_PUBLIC_ENABLE_CHAT !== 'false';

// D2 Feature Flags - MCP Rules Engine
export const FEATURE_FLAGS = {
  // MCP Rules Engine Integration
  MCP_RULES_ENGINE: process.env.NEXT_PUBLIC_FEATURE_MCP_RULES_ENGINE === 'true',

  // Deployment Phase Controls
  D2_STAGING_MODE: process.env.NEXT_PUBLIC_D2_STAGING_MODE === 'true',
  D3_PILOT_TENANT: process.env.NEXT_PUBLIC_D3_PILOT_TENANT === 'true',

  // Monitoring and Debugging
  MCP_DEBUG_LOGGING: process.env.NEXT_PUBLIC_MCP_DEBUG_LOGGING === 'true',
  MCP_METRICS_ENABLED: process.env.NEXT_PUBLIC_MCP_METRICS_ENABLED !== 'false',
} as const;

// Environment detection
export const IS_DEVELOPMENT = process.env.NODE_ENV === 'development';
export const IS_STAGING = process.env.VERCEL_ENV === 'preview' || process.env.NEXT_PUBLIC_VERCEL_ENV === 'preview';
export const IS_PRODUCTION = process.env.VERCEL_ENV === 'production' || process.env.NODE_ENV === 'production';

// Deployment phase detection
export const DEPLOYMENT_PHASE = (() => {
  if (FEATURE_FLAGS.D3_PILOT_TENANT) return 'D3_PILOT';
  if (FEATURE_FLAGS.D2_STAGING_MODE) return 'D2_STAGING';
  if (FEATURE_FLAGS.MCP_RULES_ENGINE) return 'D1_READY';
  return 'D0_DEVELOPMENT';
})();

// Limits
export const DEFAULT_PAGE_SIZE = 10;
export const MAX_FILE_SIZE = 25 * 1024 * 1024; // 25MB
