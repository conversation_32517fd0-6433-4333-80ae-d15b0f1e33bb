/**
 * Feature Flag Dashboard for D2-D7 Deployment Monitoring
 * 
 * Admin component to monitor and control MCP Rules Engine feature flags
 * during the phased deployment process.
 */

'use client';

import React from 'react';
import { useFeatureFlags } from '@/lib/featureFlags';
import { DEPLOYMENT_PHASE } from '@/lib/config';

interface FeatureFlagDashboardProps {
  tenantId?: string;
  userRole?: string;
}

export const FeatureFlagDashboard: React.FC<FeatureFlagDashboardProps> = ({
  tenantId,
  userRole
}) => {
  const { mcpFlags, deploymentInfo, isMcpEnabled, isD2Staging, isD3Pilot } = useFeatureFlags({
    tenantId,
    userRole
  });

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'D0_DEVELOPMENT': return 'bg-gray-100 text-gray-800';
      case 'D1_READY': return 'bg-blue-100 text-blue-800';
      case 'D2_STAGING': return 'bg-yellow-100 text-yellow-800';
      case 'D3_PILOT': return 'bg-orange-100 text-orange-800';
      default: return 'bg-green-100 text-green-800';
    }
  };

  const getStatusColor = (enabled: boolean) => {
    return enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6">MCP Rules Engine - Feature Flag Status</h2>
      
      {/* Deployment Phase */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Current Deployment Phase</h3>
        <div className="flex items-center space-x-4">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPhaseColor(DEPLOYMENT_PHASE)}`}>
            {DEPLOYMENT_PHASE}
          </span>
          <span className="text-gray-600">Environment: {deploymentInfo.environment}</span>
        </div>
      </div>

      {/* Feature Flag Status */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Feature Flag Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <span className="font-medium">MCP Rules Engine</span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(mcpFlags.enabled)}`}>
                {mcpFlags.enabled ? 'ENABLED' : 'DISABLED'}
              </span>
            </div>
            {tenantId && (
              <p className="text-sm text-gray-600 mt-1">Tenant: {tenantId}</p>
            )}
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <span className="font-medium">Debug Logging</span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(mcpFlags.debugLogging)}`}>
                {mcpFlags.debugLogging ? 'ON' : 'OFF'}
              </span>
            </div>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <span className="font-medium">Metrics Collection</span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(mcpFlags.metricsEnabled)}`}>
                {mcpFlags.metricsEnabled ? 'ACTIVE' : 'INACTIVE'}
              </span>
            </div>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <span className="font-medium">Staging Mode</span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(mcpFlags.stagingMode)}`}>
                {mcpFlags.stagingMode ? 'ACTIVE' : 'INACTIVE'}
              </span>
            </div>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <span className="font-medium">Pilot Tenant</span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(mcpFlags.pilotTenant)}`}>
                {mcpFlags.pilotTenant ? 'ACTIVE' : 'INACTIVE'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Phase-Specific Information */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Phase Information</h3>
        <div className="bg-gray-50 p-4 rounded-lg">
          {isD2Staging && (
            <div className="text-yellow-800">
              <h4 className="font-medium">D2 Staging Phase Active</h4>
              <p className="text-sm mt-1">
                MCP Rules Engine is enabled in staging environment for validation.
                Monitoring error rates and latency for production readiness.
              </p>
            </div>
          )}
          
          {isD3Pilot && (
            <div className="text-orange-800">
              <h4 className="font-medium">D3 Pilot Phase Active</h4>
              <p className="text-sm mt-1">
                MCP Rules Engine is enabled for pilot tenant: pilot-smith.
                Collecting real-world usage data before full rollout.
              </p>
            </div>
          )}
          
          {!isD2Staging && !isD3Pilot && mcpFlags.enabled && (
            <div className="text-green-800">
              <h4 className="font-medium">Production Phase Active</h4>
              <p className="text-sm mt-1">
                MCP Rules Engine is fully deployed and operational.
              </p>
            </div>
          )}
          
          {!mcpFlags.enabled && (
            <div className="text-gray-600">
              <h4 className="font-medium">MCP Rules Engine Disabled</h4>
              <p className="text-sm mt-1">
                Feature is currently disabled. Using fallback deadline calculation.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Performance Targets (D2 Specific) */}
      {(isD2Staging || isD3Pilot) && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Performance Targets</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="text-sm text-gray-600">Error Rate Target</div>
              <div className="text-2xl font-bold text-green-600">&lt; 1%</div>
            </div>
            <div className="p-4 border rounded-lg">
              <div className="text-sm text-gray-600">P95 Latency Target</div>
              <div className="text-2xl font-bold text-blue-600">&lt; 700ms</div>
            </div>
          </div>
        </div>
      )}

      {/* Debug Information */}
      {mcpFlags.debugLogging && (
        <div className="mt-6 p-4 bg-gray-100 rounded-lg">
          <h4 className="font-medium mb-2">Debug Information</h4>
          <pre className="text-xs text-gray-700 overflow-x-auto">
            {JSON.stringify({ mcpFlags, deploymentInfo }, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};
