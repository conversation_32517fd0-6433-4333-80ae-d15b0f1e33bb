{"version": 3, "names": ["_assert<PERSON>lassBrand", "require", "_classStaticPrivateMethodGet", "receiver", "classConstructor", "method", "assertClassBrand"], "sources": ["../../src/helpers/classStaticPrivateMethodGet.ts"], "sourcesContent": ["/* @minVersion 7.3.2 */\n\nimport assertClassBrand from \"./assertClassBrand.ts\";\nexport default function _classStaticPrivateMethodGet<T extends Function>(\n  receiver: Function,\n  classConstructor: Function,\n  method: T,\n) {\n  assertClassBrand(classConstructor, receiver);\n  return method;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,iBAAA,GAAAC,OAAA;AACe,SAASC,4BAA4BA,CAClDC,QAAkB,EAClBC,gBAA0B,EAC1BC,MAAS,EACT;EACA,IAAAC,yBAAgB,EAACF,gBAAgB,EAAED,QAAQ,CAAC;EAC5C,OAAOE,MAAM;AACf", "ignoreList": []}